<?php
require_once __DIR__ . "/../models/Post.php";

class PostController
{
    public function index()
    {
        $postModel = new Post();
        $posts = $postModel->all();
        include __DIR__ . '/../views/post_list.php';
    }

    public function create()
    {
        if ($_SERVER['REQUEST_METHOD'] === 'POST') {
            $title = $_POST['title'];
            $content = $_POST['content'];

            $postModel = new Post();
            $postModel->create($title, $content);

            header("Location: index.php");
        } else {
            include __DIR__ . '/../views/post_create.php';
        }
    }

    public function show($id)
    {
        $postModel = new Post();
        $post = $postModel->find($id);
        include __DIR__ . '/../views/post_detail.php';
    }
}
