<?php

$controller = $_GET['controller'] ?? 'post';
$action = $_GET['action'] ?? 'index';
$id = $_GET['id'] ?? null;

require_once __DIR__ . "/app/controllers/PostController.php";
$postController = new PostController();

switch ($action) {
    case 'index':
        $postController->index();
        break;
    case 'create':
        $postController->create();
        break;
    case 'show':
        $postController->show($id);
        break;
    default:
        echo "404 - Action not found";
}
