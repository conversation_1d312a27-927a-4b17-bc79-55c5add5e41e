<?php
require_once __DIR__ . '/../config/config.php';

class Database
{
    private $host;
    private $user;
    private $pass;
    private $dbname;
    public $conn;

    public function __construct()
    {
        $this->host = DB_HOST;
        $this->user = DB_USER;
        $this->pass = DB_PASS;
        $this->dbname = DB_NAME;

        $this->conn = new mysqli($this->host, $this->user, $this->pass, $this->dbname);
        if ($this->conn->connect_error) {
            die("Failed Connection: " . $this->conn->connect_error);
        }
    }
}
