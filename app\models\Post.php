<?php
require_once __DIR__ . '/../core/Database.php';

class Post
{
    private $db;

    public function __construct()
    {
        $this->db = (new Database())->conn;
    }

    public function all()
    {
        return $this->db->query("SELECT * FROM posts ORDER BY created_at DESC");
    }

    public function create($title, $content)
    {
        $stmt = $this->db->prepare("INSERT INTO posts (title, content) VALUES (?, ?)");
        $stmt->bind_param("ss", $title, $content);
        return $stmt->execute();
    }

    public function find($id)
    {
        $stmt = $this->db->prepare("SELECT * FROM posts WHERE id = ?");
        $stmt->bind_param("i", $id);
        $stmt->execute();
        return $stmt->get_result()->fetch_assoc();
    }
}
